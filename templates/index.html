<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌈 可爱聊天室 🌈</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
</head>
<body>
    <!-- 登录界面 -->
    <div id="login-container" class="container">
        <div class="login-box">
            <h1>🌟 欢迎来到可爱聊天室 🌟</h1>
            <div class="cute-avatar">🐱</div>
            <div class="input-group">
                <input type="text" id="nickname-input" placeholder="请输入你的可爱昵称..." maxlength="20">
                <button id="join-btn" class="cute-btn">🚀 进入聊天室</button>
            </div>
            <div id="login-error" class="error-message"></div>
        </div>
    </div>

    <!-- 聊天界面 -->
    <div id="chat-container" class="container hidden">
        <div class="chat-header">
            <h2>🌈 可爱聊天室 🌈</h2>
            <div class="user-info">
                <span id="current-user">👤 </span>
                <button id="logout-btn" class="logout-btn">🚪 退出</button>
            </div>
        </div>

        <div class="chat-main">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="online-users">
                    <h3>🌟 在线用户</h3>
                    <div id="users-list"></div>
                </div>
                <div class="filter-section">
                    <h3>🔍 查看记录</h3>
                    <select id="user-filter">
                        <option value="">所有消息</option>
                    </select>
                    <button id="clear-filter-btn" class="small-btn">清除筛选</button>
                </div>
            </div>

            <!-- 聊天区域 -->
            <div class="chat-area">
                <div id="messages-container" class="messages">
                    <div class="welcome-message">
                        🎉 欢迎来到聊天室！开始愉快的聊天吧~ 🎉
                    </div>
                </div>

                <div class="input-area">
                    <div class="message-input-group">
                        <input type="text" id="message-input" placeholder="输入你想说的话..." maxlength="500">
                        <button id="send-btn" class="send-btn">💌 发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
</body>
</html>
