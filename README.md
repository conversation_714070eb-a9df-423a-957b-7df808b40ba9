# 🌈 可爱聊天室 🌈

一个基于Flask和Socket.IO的实时多人在线聊天室，具有可爱的卡通风格界面。

## ✨ 功能特点

- 🎯 **多人实时聊天** - 支持多用户同时在线聊天
- 🏷️ **昵称登录** - 无需密码，使用自定义昵称即可加入
- ⏰ **时间戳显示** - 每条消息都显示发送时间
- 🔍 **按昵称筛选** - 可以查看特定用户的聊天记录
- 🎨 **卡通界面** - 可爱的UI设计，丰富的动画效果
- 👥 **在线用户列表** - 实时显示当前在线用户
- 📱 **响应式设计** - 支持移动设备访问

## 🛠️ 技术栈

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: Python Flask + Flask-SocketIO
- **实时通信**: Socket.IO
- **部署**: Gunicorn + Gevent
- **存储**: 内存存储（程序运行期间）

## 🚀 快速开始

### 环境要求

- Python 3.7+
- pip3

### 安装和运行

1. **克隆或下载项目文件**

2. **给启动脚本添加执行权限**
   ```bash
   chmod +x start.sh
   ```

3. **运行启动脚本**
   ```bash
   ./start.sh
   ```

4. **访问聊天室**
   - 本地访问: http://localhost:7101
   - 公网访问: http://**************:7101

### 手动启动

如果启动脚本无法运行，可以手动执行以下命令：

```bash
# 安装依赖
pip3 install -r requirements.txt

# 启动服务器
gunicorn -c gunicorn_config.py app:app
```

## 📁 项目结构

```
chatroom2/
├── app.py                 # Flask主应用
├── requirements.txt       # Python依赖包
├── gunicorn_config.py    # Gunicorn配置
├── start.sh              # 启动脚本
├── README.md             # 项目说明
├── static/               # 静态资源
│   ├── css/
│   │   └── style.css     # 样式文件
│   └── js/
│       └── chat.js       # JavaScript功能
└── templates/            # HTML模板
    └── index.html        # 主页面
```

## 🎮 使用说明

1. **加入聊天室**
   - 打开网页后输入你的昵称
   - 点击"🚀 进入聊天室"按钮

2. **发送消息**
   - 在底部输入框输入消息
   - 按回车键或点击"💌 发送"按钮

3. **查看特定用户消息**
   - 在左侧用户列表点击用户名
   - 或在筛选下拉框中选择用户

4. **清除筛选**
   - 点击"清除筛选"按钮查看所有消息

## 🔧 配置说明

### 服务器配置

- **IP地址**: 0.0.0.0 (监听所有网络接口)
- **端口**: 7101
- **工作进程**: CPU核心数 × 2 + 1
- **工作进程类型**: gevent (异步)

### 自定义配置

如需修改服务器配置，请编辑 `gunicorn_config.py` 文件。

## 🎨 界面特色

- 🌈 渐变背景和可爱配色
- 🎭 丰富的Emoji表情
- ✨ 平滑的动画效果
- 🎪 圆角设计和阴影效果
- 📱 移动端友好的响应式布局

## 📝 注意事项

- 聊天记录仅在程序运行期间保存，重启后会清空
- 昵称不能重复，需要选择唯一的昵称
- 建议使用现代浏览器以获得最佳体验
- 服务器重启时所有用户会自动断开连接

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

---

🎉 **享受愉快的聊天时光！** 🎉
