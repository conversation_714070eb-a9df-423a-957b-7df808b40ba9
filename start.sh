#!/bin/bash

# 可爱聊天室启动脚本

echo "🌟 正在启动可爱聊天室... 🌟"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ 错误: 未找到pip3，请先安装pip3"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "🔧 正在创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔄 正在激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📦 正在安装依赖包..."
pip install -r requirements.txt

# 检查安装是否成功
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败，请检查网络连接和权限"
    exit 1
fi

echo "🚀 正在启动服务器..."
echo "📍 服务器地址: http://**************:7101"
echo "🎉 聊天室即将开启，准备好愉快聊天吧！"

# 使用gunicorn启动应用（在虚拟环境中）
source venv/bin/activate && gunicorn -c gunicorn_config.py app:app

echo "👋 聊天室已关闭，期待下次见面！"
