// 可爱聊天室 JavaScript 功能
class ChatRoom {
    constructor() {
        this.socket = io();
        this.currentUser = '';
        this.allMessages = [];
        this.filteredUser = '';

        this.initializeElements();
        this.bindEvents();
        this.setupSocketEvents();
    }

    initializeElements() {
        // 登录界面元素
        this.loginContainer = document.getElementById('login-container');
        this.chatContainer = document.getElementById('chat-container');
        this.nicknameInput = document.getElementById('nickname-input');
        this.joinBtn = document.getElementById('join-btn');
        this.loginError = document.getElementById('login-error');

        // 聊天界面元素
        this.currentUserSpan = document.getElementById('current-user');
        this.logoutBtn = document.getElementById('logout-btn');
        this.messagesContainer = document.getElementById('messages-container');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
        this.usersList = document.getElementById('users-list');
        this.userFilter = document.getElementById('user-filter');
        this.clearFilterBtn = document.getElementById('clear-filter-btn');
    }

    bindEvents() {
        // 登录事件
        this.joinBtn.addEventListener('click', () => this.joinChat());
        this.nicknameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.joinChat();
        });

        // 聊天事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        // 其他事件
        this.logoutBtn.addEventListener('click', () => this.logout());
        this.userFilter.addEventListener('change', () => this.filterMessages());
        this.clearFilterBtn.addEventListener('click', () => this.clearFilter());
    }

    setupSocketEvents() {
        this.socket.on('connect', () => {
            console.log('连接到服务器');
        });

        this.socket.on('disconnect', () => {
            console.log('与服务器断开连接');
        });

        this.socket.on('error', (data) => {
            this.showError(data.message);
        });

        this.socket.on('chat_history', (data) => {
            this.allMessages = data.messages;
            this.displayMessages(this.allMessages);
        });

        this.socket.on('new_message', (message) => {
            this.allMessages.push(message);
            if (!this.filteredUser || message.nickname === this.filteredUser) {
                this.addMessage(message);
            }
        });

        this.socket.on('user_joined', (data) => {
            this.addSystemMessage(`🎉 ${data.nickname} 加入了聊天室`);
        });

        this.socket.on('user_left', (data) => {
            this.addSystemMessage(`👋 ${data.nickname} 离开了聊天室`);
        });

        this.socket.on('update_user_list', (data) => {
            this.updateUsersList(data.users);
            this.updateUserFilter(data.users);
        });

        this.socket.on('user_messages', (data) => {
            this.displayMessages(data.messages);
            this.filteredUser = data.nickname;
        });
    }

    joinChat() {
        const nickname = this.nicknameInput.value.trim();
        if (!nickname) {
            this.showError('请输入昵称');
            return;
        }

        this.socket.emit('join_chat', { nickname: nickname });
        this.currentUser = nickname;
        this.currentUserSpan.textContent = `👤 ${nickname}`;

        // 切换到聊天界面
        this.loginContainer.classList.add('hidden');
        this.chatContainer.classList.remove('hidden');
        this.messageInput.focus();
    }

    sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        this.socket.emit('send_message', { message: message });
        this.messageInput.value = '';
        this.messageInput.focus();
    }

    logout() {
        this.socket.disconnect();
        this.chatContainer.classList.add('hidden');
        this.loginContainer.classList.remove('hidden');
        this.nicknameInput.value = '';
        this.nicknameInput.focus();
        this.clearMessages();
        this.currentUser = '';
        this.allMessages = [];
        this.filteredUser = '';

        // 重新连接
        this.socket.connect();
    }

    showError(message) {
        this.loginError.textContent = message;
        this.loginError.style.display = 'block';
        setTimeout(() => {
            this.loginError.style.display = 'none';
        }, 3000);
    }

    addMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.nickname === this.currentUser ? 'own' : 'other'}`;

        messageDiv.innerHTML = `
            <div class="message-header">
                ${message.nickname === this.currentUser ? '我' : message.nickname} • ${message.timestamp}
            </div>
            <div class="message-content">${this.escapeHtml(message.message)}</div>
        `;

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addSystemMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'system-message';
        messageDiv.textContent = message;
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    displayMessages(messages) {
        this.clearMessages();
        messages.forEach(message => this.addMessage(message));
    }

    clearMessages() {
        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
        this.messagesContainer.innerHTML = '';
        if (welcomeMessage && !this.filteredUser) {
            this.messagesContainer.appendChild(welcomeMessage);
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    updateUsersList(users) {
        this.usersList.innerHTML = '';
        users.forEach(user => {
            const userDiv = document.createElement('div');
            userDiv.className = 'user-item';
            userDiv.textContent = `🌟 ${user}`;
            userDiv.addEventListener('click', () => {
                this.userFilter.value = user;
                this.filterMessages();
            });
            this.usersList.appendChild(userDiv);
        });
    }

    updateUserFilter(users) {
        const currentValue = this.userFilter.value;
        this.userFilter.innerHTML = '<option value="">所有消息</option>';
        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user;
            option.textContent = user;
            this.userFilter.appendChild(option);
        });
        this.userFilter.value = currentValue;
    }

    filterMessages() {
        const selectedUser = this.userFilter.value;
        if (selectedUser) {
            this.filteredUser = selectedUser;
            const filteredMessages = this.allMessages.filter(msg => msg.nickname === selectedUser);
            this.displayMessages(filteredMessages);
        } else {
            this.clearFilter();
        }
    }

    clearFilter() {
        this.filteredUser = '';
        this.userFilter.value = '';
        this.displayMessages(this.allMessages);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化聊天室
document.addEventListener('DOMContentLoaded', () => {
    new ChatRoom();
});
