/* 可爱聊天室样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hidden {
    display: none !important;
}

/* 登录界面样式 */
.login-box {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 25px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
    width: 90%;
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.login-box h1 {
    color: #6c5ce7;
    margin-bottom: 20px;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.cute-avatar {
    font-size: 60px;
    margin: 20px 0;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.input-group {
    margin: 20px 0;
}

#nickname-input {
    width: 100%;
    padding: 15px;
    border: 3px solid #ddd;
    border-radius: 25px;
    font-size: 16px;
    margin-bottom: 15px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

#nickname-input:focus {
    border-color: #6c5ce7;
    box-shadow: 0 0 15px rgba(108, 92, 231, 0.3);
    transform: scale(1.02);
}

.cute-btn {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.cute-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-weight: bold;
    padding: 10px;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 15px;
    display: none;
}

/* 聊天界面样式 */
#chat-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    margin: 0;
    border-radius: 0;
}

.chat-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-header h2 {
    margin: 0;
    font-size: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

#current-user {
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 20px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.chat-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background: #f8f9ff;
    border-right: 2px solid #e0e6ff;
    padding: 20px;
    overflow-y: auto;
}

.sidebar h3 {
    color: #6c5ce7;
    margin-bottom: 15px;
    font-size: 16px;
    text-align: center;
}

.online-users {
    margin-bottom: 30px;
}

#users-list {
    max-height: 200px;
    overflow-y: auto;
}

.user-item {
    background: white;
    padding: 10px;
    margin: 5px 0;
    border-radius: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #6c5ce7;
}

.user-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.filter-section select {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 15px;
    margin-bottom: 10px;
    background: white;
    outline: none;
}

.small-btn {
    width: 100%;
    padding: 8px;
    background: #74b9ff;
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.small-btn:hover {
    background: #0984e3;
    transform: translateY(-2px);
}

/* 聊天区域样式 */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: linear-gradient(to bottom, #f8f9ff, #ffffff);
}

.welcome-message {
    text-align: center;
    color: #6c5ce7;
    font-size: 18px;
    margin: 20px 0;
    padding: 20px;
    background: rgba(108, 92, 231, 0.1);
    border-radius: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.message {
    margin: 15px 0;
    padding: 15px;
    border-radius: 20px;
    max-width: 80%;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
    position: relative;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.own {
    background: linear-gradient(45deg, #6c5ce7, #a29bfe);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.message.other {
    background: #f1f2f6;
    color: #2d3436;
    margin-right: auto;
    border-bottom-left-radius: 5px;
    border-left: 4px solid #6c5ce7;
}

.message-header {
    font-size: 12px;
    margin-bottom: 5px;
    opacity: 0.8;
}

.message.own .message-header {
    color: rgba(255, 255, 255, 0.9);
}

.message.other .message-header {
    color: #6c5ce7;
    font-weight: bold;
}

.message-content {
    font-size: 14px;
    line-height: 1.4;
}

.system-message {
    text-align: center;
    color: #74b9ff;
    font-style: italic;
    margin: 10px 0;
    padding: 10px;
    background: rgba(116, 185, 255, 0.1);
    border-radius: 15px;
}

/* 输入区域样式 */
.input-area {
    padding: 20px;
    background: #f8f9ff;
    border-top: 2px solid #e0e6ff;
}

.message-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

#message-input {
    flex: 1;
    padding: 15px 20px;
    border: 3px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    background: white;
}

#message-input:focus {
    border-color: #6c5ce7;
    box-shadow: 0 0 15px rgba(108, 92, 231, 0.3);
}

.send-btn {
    background: linear-gradient(45deg, #00b894, #00cec9);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.send-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.send-btn:active {
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .chat-header h2 {
        font-size: 16px;
    }

    .message {
        max-width: 90%;
    }

    .login-box {
        padding: 30px 20px;
    }
}
