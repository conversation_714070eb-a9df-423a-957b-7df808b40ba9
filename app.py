from flask import Flask, render_template, request
from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room, leave_room
from datetime import datetime
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'chatroom_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# 存储聊天记录和在线用户
chat_history = []
online_users = {}

@app.route('/')
def index():
    return render_template('index.html')

@socketio.on('connect')
def on_connect():
    print(f'用户连接: {request.sid}')

@socketio.on('disconnect')
def on_disconnect():
    print(f'用户断开连接: {request.sid}')
    # 从在线用户列表中移除
    if request.sid in online_users:
        nickname = online_users[request.sid]
        del online_users[request.sid]
        # 通知其他用户有人离开
        emit('user_left', {'nickname': nickname}, broadcast=True)
        emit('update_user_list', {'users': list(online_users.values())}, broadcast=True)

@socketio.on('join_chat')
def on_join_chat(data):
    nickname = data['nickname'].strip()
    if not nickname:
        emit('error', {'message': '昵称不能为空'})
        return
    
    # 检查昵称是否已被使用
    if nickname in online_users.values():
        emit('error', {'message': '昵称已被使用，请选择其他昵称'})
        return
    
    # 添加用户到在线列表
    online_users[request.sid] = nickname
    
    # 发送历史消息给新用户
    emit('chat_history', {'messages': chat_history})
    
    # 通知所有用户有新用户加入
    emit('user_joined', {'nickname': nickname}, broadcast=True)
    
    # 更新在线用户列表
    emit('update_user_list', {'users': list(online_users.values())}, broadcast=True)
    
    print(f'用户 {nickname} 加入聊天室')

@socketio.on('send_message')
def on_send_message(data):
    if request.sid not in online_users:
        emit('error', {'message': '请先加入聊天室'})
        return
    
    nickname = online_users[request.sid]
    message = data['message'].strip()
    
    if not message:
        return
    
    # 创建消息对象
    message_obj = {
        'nickname': nickname,
        'message': message,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'id': len(chat_history) + 1
    }
    
    # 保存到聊天记录
    chat_history.append(message_obj)
    
    # 广播消息给所有用户
    emit('new_message', message_obj, broadcast=True)
    
    print(f'{nickname}: {message}')

@socketio.on('get_user_messages')
def on_get_user_messages(data):
    target_nickname = data['nickname']
    user_messages = [msg for msg in chat_history if msg['nickname'] == target_nickname]
    emit('user_messages', {'nickname': target_nickname, 'messages': user_messages})

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=7101, debug=True)
