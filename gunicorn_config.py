# Gunicorn 配置文件
import multiprocessing

# 服务器绑定地址和端口
bind = "0.0.0.0:7101"

# 工作进程数量
workers = multiprocessing.cpu_count() * 2 + 1

# 工作进程类型，使用gevent异步工作进程
worker_class = "gevent"

# 每个工作进程的协程数量
worker_connections = 1000

# 超时设置
timeout = 30
keepalive = 2

# 日志设置
accesslog = "-"
errorlog = "-"
loglevel = "info"

# 进程名称
proc_name = "chatroom_app"

# 预加载应用
preload_app = True

# 最大请求数，防止内存泄漏
max_requests = 1000
max_requests_jitter = 50
